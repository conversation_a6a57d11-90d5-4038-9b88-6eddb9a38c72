#!/bin/bash
# SSH连接测试脚本

SERVER_IP="*************"
echo "=== SSH连接测试 ==="
echo "服务器IP: $SERVER_IP"
echo ""

# 测试常见SSH端口
PORTS=(22 2022 8022 39000)

echo "测试SSH端口连通性..."
for port in "${PORTS[@]}"; do
    echo -n "测试端口 $port: "
    timeout 5 bash -c "echo >/dev/tcp/$SERVER_IP/$port" 2>/dev/null
    if [ $? -eq 0 ]; then
        echo "✓ 开放"
        echo "尝试SSH连接: ssh -p $port root@$SERVER_IP"
    else
        echo "✗ 关闭"
    fi
done

echo ""
echo "=== 连接建议 ==="
echo "1. 首先尝试默认端口: ssh root@$SERVER_IP"
echo "2. 如果失败，尝试其他端口: ssh -p 2022 root@$SERVER_IP"
echo "3. 在宝塔面板中查看SSH配置获取准确信息"
echo ""
echo "=== 宝塔面板地址 ==="
echo "http://$SERVER_IP:33565"
