# 部署指南

## 环境要求

### 服务器环境
- **操作系统**: Linux (Ubuntu 20.04+ / CentOS 7+)
- **Web服务器**: Nginx 1.18+
- **PHP**: PHP 8.1+ (推荐 8.1)
- **数据库**: MySQL 8.0+ / MariaDB 10.5+
- **缓存**: Redis 6.0+
- **队列**: Redis (Think Queue)

### PHP扩展要求
```bash
# 必需扩展
php8.1-fpm
php8.1-mysql
php8.1-redis
php8.1-curl
php8.1-json
php8.1-mbstring
php8.1-xml
php8.1-zip
php8.1-gd
php8.1-bcmath
php8.1-openssl
php8.1-pdo
```

### Node.js环境
- **Node.js**: 16.0+ (用于前端构建)
- **npm**: 8.0+ 或 yarn 1.22+

## 部署架构

### 推荐部署架构
```
负载均衡器 (Nginx)
    ↓
Web服务器集群
├── Web Server 1 (API + Admin)
├── Web Server 2 (API + Admin)
└── Web Server 3 (API + Admin)
    ↓
数据库集群
├── MySQL Master
├── MySQL Slave 1
└── MySQL Slave 2
    ↓
缓存集群
├── Redis Master
└── Redis Slave
```

### 单机部署架构
```
服务器 (单机)
├── Nginx (Web服务器)
├── PHP-FPM (PHP处理)
├── MySQL (数据库)
├── Redis (缓存/队列)
└── 静态文件存储
```

## API后端部署

### 1. 代码部署
```bash
# 创建项目目录
sudo mkdir -p /www/wwwroot/facai16_api
cd /www/wwwroot/facai16_api

# 克隆代码 (或上传代码包)
git clone <repository_url> .

# 设置权限
sudo chown -R www-data:www-data /www/wwwroot/facai16_api
sudo chmod -R 755 /www/wwwroot/facai16_api
sudo chmod -R 777 /www/wwwroot/facai16_api/runtime
sudo chmod -R 777 /www/wwwroot/facai16_api/public/uploads
```

### 2. 依赖安装
```bash
# 安装Composer依赖
composer install --no-dev --optimize-autoloader

# 清理缓存
php think clear
```

### 3. 环境配置
```bash
# 复制环境配置文件
cp .env.example .env

# 编辑环境配置
vim .env
```

### 4. 数据库配置
```bash
# 创建数据库
mysql -u root -p
CREATE DATABASE facai16 CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

# 导入数据库结构
mysql -u root -p facai16 < database/facai16.sql

# 运行数据库迁移
php think migrate:run
```

### 5. Nginx配置
```nginx
server {
    listen 80;
    server_name api.facai16.com;
    root /www/wwwroot/facai16_api/public;
    index index.php index.html;

    # 日志配置
    access_log /var/log/nginx/facai16_api.access.log;
    error_log /var/log/nginx/facai16_api.error.log;

    # PHP处理
    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.1-fpm.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
    }

    # 静态文件缓存
    location ~* \.(jpg|jpeg|png|gif|ico|css|js)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # 安全配置
    location ~ /\.ht {
        deny all;
    }
}
```

## 管理后台部署

### 1. 构建前端
```bash
cd facai16_admin

# 安装依赖
npm install

# 构建生产版本
npm run build:facai
```

### 2. 部署静态文件
```bash
# 创建部署目录
sudo mkdir -p /www/wwwroot/facai16_admin

# 复制构建文件
sudo cp -r dist/* /www/wwwroot/facai16_admin/

# 设置权限
sudo chown -R www-data:www-data /www/wwwroot/facai16_admin
```

### 3. Nginx配置
```nginx
server {
    listen 80;
    server_name admin.facai16.com;
    root /www/wwwroot/facai16_admin;
    index index.html;

    # SPA路由支持
    location / {
        try_files $uri $uri/ /index.html;
    }

    # API代理
    location /facai16 {
        proxy_pass http://api.facai16.com/admin.php;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }

    # 静态文件缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

## 移动端APP部署

### 1. H5版本部署
```bash
cd facai16_app

# 安装依赖
npm install

# 构建H5版本
npm run build:h5

# 部署到服务器
sudo cp -r dist/build/h5/* /www/wwwroot/facai16_h5/
```

### 2. APP打包
```bash
# 构建APP版本
npm run build:app-plus

# 使用HBuilderX打包
# 或使用云打包服务
```

## 队列服务部署

### 1. 队列配置
```bash
# 创建队列服务脚本
sudo vim /etc/systemd/system/facai16-queue.service
```

```ini
[Unit]
Description=Facai16 Queue Worker
After=network.target

[Service]
Type=simple
User=www-data
WorkingDirectory=/www/wwwroot/facai16_api
ExecStart=/usr/bin/php think queue:work
Restart=always
RestartSec=3

[Install]
WantedBy=multi-user.target
```

### 2. 启动队列服务
```bash
# 启用服务
sudo systemctl enable facai16-queue

# 启动服务
sudo systemctl start facai16-queue

# 查看状态
sudo systemctl status facai16-queue
```

## SSL证书配置

### 1. 申请SSL证书
```bash
# 使用Let's Encrypt
sudo apt install certbot python3-certbot-nginx

# 申请证书
sudo certbot --nginx -d api.facai16.com
sudo certbot --nginx -d admin.facai16.com
```

### 2. 自动续期
```bash
# 添加定时任务
sudo crontab -e

# 每月1号凌晨2点检查续期
0 2 1 * * /usr/bin/certbot renew --quiet
```

## 监控配置

### 1. 日志监控
```bash
# 配置日志轮转
sudo vim /etc/logrotate.d/facai16

/var/log/nginx/facai16*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 644 www-data www-data
    postrotate
        systemctl reload nginx
    endscript
}
```

### 2. 性能监控
```bash
# 安装监控工具
sudo apt install htop iotop nethogs

# 配置MySQL慢查询日志
sudo vim /etc/mysql/mysql.conf.d/mysqld.cnf

[mysqld]
slow_query_log = 1
slow_query_log_file = /var/log/mysql/slow.log
long_query_time = 2
```

## 备份策略

### 1. 数据库备份
```bash
# 创建备份脚本
sudo vim /usr/local/bin/backup_facai16.sh

#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backup/facai16"
mkdir -p $BACKUP_DIR

# 备份数据库
mysqldump -u root -p facai16 > $BACKUP_DIR/facai16_$DATE.sql

# 压缩备份文件
gzip $BACKUP_DIR/facai16_$DATE.sql

# 删除7天前的备份
find $BACKUP_DIR -name "*.sql.gz" -mtime +7 -delete
```

### 2. 文件备份
```bash
# 备份代码和上传文件
tar -czf $BACKUP_DIR/files_$DATE.tar.gz /www/wwwroot/facai16_api/public/uploads
```

### 3. 定时备份
```bash
# 添加定时任务
sudo crontab -e

# 每天凌晨3点备份
0 3 * * * /usr/local/bin/backup_facai16.sh
```

## 安全配置

### 1. 防火墙配置
```bash
# 配置UFW防火墙
sudo ufw enable
sudo ufw allow 22/tcp
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw deny 3306/tcp
sudo ufw deny 6379/tcp
```

### 2. 文件权限
```bash
# 设置安全的文件权限
sudo find /www/wwwroot/facai16_api -type f -exec chmod 644 {} \;
sudo find /www/wwwroot/facai16_api -type d -exec chmod 755 {} \;
sudo chmod -R 777 /www/wwwroot/facai16_api/runtime
```

### 3. 隐藏敏感信息
```nginx
# Nginx隐藏版本信息
server_tokens off;

# 隐藏PHP版本信息
fastcgi_param SERVER_SOFTWARE nginx;
```
