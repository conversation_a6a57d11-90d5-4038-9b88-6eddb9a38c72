<?php
// 修复后的 RelationTrait.php 代码片段

public function relationshipTransfer($fromUserId, $toUserId): array
{
    $UserRepo     = new UserRepository();
    // 修复：直接使用ID查询，而不是当作phone查询
    $fromUserInfo = $UserRepo->findById($fromUserId);
    $toUserInfo   = $UserRepo->findById($toUserId);

    // 验证用户是否存在
    if (!$fromUserInfo) {
        return Result::fail('来源用户为空');
    }

    if (!$toUserInfo) {
        return Result::fail('转移用户为空');
    }

    $fromUserId = $fromUserInfo['id'];
    $toUserId   = $toUserInfo['id'];

    // 检查循环引用
    if ($this->isCircularReference($fromUserId, $toUserId)) {
        return Result::fail('循环引用!!');
    }

    Db::startTrans();

    try {
        // 1. 更新被转移用户的上级关系
        $this->updateUserRelations($fromUserId, $toUserId);

        // 2. 递归更新所有下级用户的关系
        $this->updateAllSubordinates($fromUserId);

        // 3. 重新赋值 v1 v2 v3
        $this->updateUserLevel($fromUserId, $toUserId);

        Db::commit();

        return Result::success();

    } catch (\Exception $e) {
        Db::rollback();
        return Result::fail('转移失败: ' . $e->getMessage());
    }
}
