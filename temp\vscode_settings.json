{
    // VSCode工作区设置 (.vscode/settings.json)
    
    // PHP配置
    "php.validate.executablePath": "/usr/bin/php",
    "php.debug.executablePath": "/usr/bin/php",
    
    // 文件监听
    "files.watcherExclude": {
        "**/node_modules/**": true,
        "**/.git/**": true,
        "**/vendor/**": true,
        "**/storage/logs/**": true,
        "**/storage/framework/cache/**": true
    },
    
    // 自动保存
    "files.autoSave": "afterDelay",
    "files.autoSaveDelay": 1000,
    
    // SFTP扩展配置
    "sftp.printDebugLog": true,
    "sftp.downloadOnOpen": false,
    "sftp.confirm.download": false,
    
    // 排除文件
    "files.exclude": {
        "**/node_modules": true,
        "**/vendor": true,
        "**/.git": true,
        "**/storage/logs": true
    }
}
