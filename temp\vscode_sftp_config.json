{
    "name": "facai16-dev-server",
    "host": "your-dev-server-ip",
    "protocol": "sftp",
    "port": 22,
    "username": "your-username",
    "password": "your-password",
    // 或者使用私钥认证（推荐）
    // "privateKeyPath": "~/.ssh/id_rsa",
    // "passphrase": "your-passphrase",
    
    "remotePath": "/var/www/html/facai16",
    "localPath": "./",
    
    // 同步配置
    "uploadOnSave": true,
    "useTempFile": false,
    "openSsh": false,
    
    // 忽略文件配置
    "ignore": [
        ".vscode",
        ".git",
        ".DS_Store",
        "node_modules",
        "*.log",
        ".env.local",
        "temp/**"
    ],
    
    // 监听文件变化
    "watcher": {
        "files": "**/*",
        "autoUpload": true,
        "autoDelete": false
    },
    
    // 同步选项
    "syncOption": {
        "delete": false,
        "skipCreate": false,
        "ignoreExisting": false,
        "update": true
    }
}
