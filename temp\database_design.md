# 数据库设计文档

## 数据库概览

### 主要数据表

#### 用户相关表
- `users` - 用户基础信息表
- `user_levels` - 用户等级表
- `user_relations` - 用户关系表
- `banks` - 用户银行卡表
- `user_auths` - 用户认证表

#### 项目相关表
- `items` - 项目信息表
- `item_records` - 投资记录表
- `item_levels` - 项目等级表

#### 资金相关表
- `money_logs` - 资金流水表
- `recharge_orders` - 充值订单表
- `withdrawals` - 提现申请表
- `transfers` - 转账记录表

#### 系统相关表
- `admins` - 管理员表
- `admin_roles` - 管理员角色表
- `menus` - 菜单权限表
- `configs` - 系统配置表
- `logs` - 系统日志表

#### 运营相关表
- `articles` - 文章表
- `notices` - 公告表
- `banners` - 轮播图表
- `products` - 商品表
- `orders` - 订单表

## 核心表结构设计

### users - 用户表
```sql
CREATE TABLE `users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) DEFAULT NULL COMMENT '用户名',
  `phone` varchar(20) NOT NULL COMMENT '手机号',
  `password` varchar(255) NOT NULL COMMENT '登录密码',
  `pay_password` varchar(255) DEFAULT NULL COMMENT '支付密码',
  `invite` varchar(20) NOT NULL COMMENT '邀请码',
  `invite_user_id` int(11) DEFAULT 0 COMMENT '邀请人ID',
  `real_name` varchar(50) DEFAULT NULL COMMENT '真实姓名',
  `id_card` varchar(20) DEFAULT NULL COMMENT '身份证号',
  `money` decimal(10,2) DEFAULT 0.00 COMMENT '余额',
  `frozen_money` decimal(10,2) DEFAULT 0.00 COMMENT '冻结金额',
  `total_recharge` decimal(10,2) DEFAULT 0.00 COMMENT '总充值',
  `total_withdrawal` decimal(10,2) DEFAULT 0.00 COMMENT '总提现',
  `v1_id` int(11) DEFAULT 0 COMMENT '一级上级ID',
  `v1_name` varchar(50) DEFAULT NULL COMMENT '一级上级姓名',
  `v2_id` int(11) DEFAULT 0 COMMENT '二级上级ID',
  `v2_name` varchar(50) DEFAULT NULL COMMENT '二级上级姓名',
  `v3_id` int(11) DEFAULT 0 COMMENT '三级上级ID',
  `v3_name` varchar(50) DEFAULT NULL COMMENT '三级上级姓名',
  `level` int(11) DEFAULT 1 COMMENT '用户等级',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态 1正常 0禁用',
  `is_auth` tinyint(1) DEFAULT 0 COMMENT '是否实名认证',
  `create_time` int(11) DEFAULT NULL COMMENT '创建时间',
  `update_time` int(11) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `phone` (`phone`),
  UNIQUE KEY `invite` (`invite`),
  KEY `invite_user_id` (`invite_user_id`),
  KEY `v1_id` (`v1_id`),
  KEY `v2_id` (`v2_id`),
  KEY `v3_id` (`v3_id`)
) COMMENT='用户表';
```

### items - 项目表
```sql
CREATE TABLE `items` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(255) NOT NULL COMMENT '项目标题',
  `description` text COMMENT '项目描述',
  `image` varchar(255) DEFAULT NULL COMMENT '项目图片',
  `min_money` decimal(10,2) NOT NULL COMMENT '最小投资金额',
  `max_money` decimal(10,2) NOT NULL COMMENT '最大投资金额',
  `rate` decimal(5,2) NOT NULL COMMENT '收益率(%)',
  `cycle` int(11) NOT NULL COMMENT '投资周期(天)',
  `total_amount` decimal(15,2) DEFAULT 0.00 COMMENT '项目总额',
  `current_amount` decimal(15,2) DEFAULT 0.00 COMMENT '当前投资额',
  `user_limit` int(11) DEFAULT 0 COMMENT '用户投资次数限制',
  `level_limit` int(11) DEFAULT 0 COMMENT '用户等级限制',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态 1正常 0禁用',
  `sort` int(11) DEFAULT 0 COMMENT '排序',
  `create_time` int(11) DEFAULT NULL,
  `update_time` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `status` (`status`),
  KEY `sort` (`sort`)
) COMMENT='项目表';
```

### money_logs - 资金流水表
```sql
CREATE TABLE `money_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `type` tinyint(2) NOT NULL COMMENT '类型 1充值 2提现 3投资 4收益 5转账出 6转账入',
  `money` decimal(10,2) NOT NULL COMMENT '金额',
  `before_money` decimal(10,2) NOT NULL COMMENT '变动前余额',
  `after_money` decimal(10,2) NOT NULL COMMENT '变动后余额',
  `order_id` varchar(50) DEFAULT NULL COMMENT '关联订单ID',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `create_time` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `type` (`type`),
  KEY `create_time` (`create_time`)
) COMMENT='资金流水表';
```

### item_records - 投资记录表
```sql
CREATE TABLE `item_records` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `item_id` int(11) NOT NULL COMMENT '项目ID',
  `money` decimal(10,2) NOT NULL COMMENT '投资金额',
  `rate` decimal(5,2) NOT NULL COMMENT '收益率',
  `cycle` int(11) NOT NULL COMMENT '投资周期',
  `profit` decimal(10,2) DEFAULT 0.00 COMMENT '预期收益',
  `status` tinyint(1) DEFAULT 0 COMMENT '状态 0进行中 1已完成 2已取消',
  `start_time` int(11) DEFAULT NULL COMMENT '开始时间',
  `end_time` int(11) DEFAULT NULL COMMENT '结束时间',
  `finish_time` int(11) DEFAULT NULL COMMENT '完成时间',
  `create_time` int(11) DEFAULT NULL,
  `update_time` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `item_id` (`item_id`),
  KEY `status` (`status`)
) COMMENT='投资记录表';
```

## 表关系设计

### 用户关系链
```
users.id ← users.invite_user_id (邀请关系)
users.id ← users.v1_id (一级上级)
users.id ← users.v2_id (二级上级)  
users.id ← users.v3_id (三级上级)
```

### 投资关系链
```
users.id → item_records.user_id
items.id → item_records.item_id
item_records.id → money_logs.order_id
```

### 资金关系链
```
users.id → money_logs.user_id
users.id → recharge_orders.user_id
users.id → withdrawals.user_id
users.id → transfers.from_user_id
users.id → transfers.to_user_id
```

## 索引设计

### 主要索引
- 用户表: `phone`, `invite`, `invite_user_id`, `v1_id`, `v2_id`, `v3_id`
- 资金流水: `user_id`, `type`, `create_time`
- 投资记录: `user_id`, `item_id`, `status`
- 项目表: `status`, `sort`

### 复合索引
- `money_logs(user_id, type, create_time)` - 用户资金流水查询
- `item_records(user_id, status, create_time)` - 用户投资记录查询
- `users(status, level, create_time)` - 用户列表查询

## 数据完整性约束

### 外键约束
- `item_records.user_id` → `users.id`
- `item_records.item_id` → `items.id`
- `money_logs.user_id` → `users.id`
- `banks.user_id` → `users.id`

### 唯一约束
- `users.phone` - 手机号唯一
- `users.invite` - 邀请码唯一
- `admins.username` - 管理员用户名唯一

### 检查约束
- `users.money >= 0` - 余额不能为负
- `items.rate > 0` - 收益率必须大于0
- `item_records.money > 0` - 投资金额必须大于0

## 分表策略

### 大数据表分表
- `money_logs` - 按月分表 `money_logs_202501`
- `item_records` - 按年分表 `item_records_2025`
- `logs` - 按月分表 `logs_202501`

### 分表规则
```sql
-- 资金流水按月分表
CREATE TABLE `money_logs_202501` LIKE `money_logs`;

-- 投资记录按年分表  
CREATE TABLE `item_records_2025` LIKE `item_records`;
```
