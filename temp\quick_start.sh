#!/bin/bash
# 快速启动开发环境脚本

echo "=== VSCode开发环境快速配置 ==="

# 1. 创建VSCode配置目录
mkdir -p .vscode

# 2. 复制配置文件
echo "复制VSCode配置文件..."
cp temp/vscode_sftp_config.json .vscode/sftp.json
cp temp/vscode_settings.json .vscode/settings.json
cp temp/vscode_launch.json .vscode/launch.json

# 3. 提示用户修改配置
echo ""
echo "请修改以下配置文件中的服务器信息："
echo "1. .vscode/sftp.json - 修改服务器IP、用户名、密码"
echo "2. .vscode/launch.json - 确认路径映射正确"
echo ""

# 4. 检查必要的扩展
echo "请确保已安装以下VSCode扩展："
echo "- SFTP (by Natizyskunk)"
echo "- PHP Debug (by Xdebug)"
echo "- PHP Intelephense"
echo ""

# 5. 提示下一步操作
echo "配置完成后的操作步骤："
echo "1. 在VSCode中打开项目"
echo "2. 按Ctrl+Shift+P，输入'SFTP: Open SSH in Terminal'连接服务器"
echo "3. 执行服务器配置脚本：sudo ./temp/dev_server_setup.sh"
echo "4. 开始开发！"

echo ""
echo "=== 配置完成 ==="
