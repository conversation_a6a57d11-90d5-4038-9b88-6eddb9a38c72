# Facai16 项目文档索引

## 📋 文档概览

本文档集合详细描述了Facai16金融投资平台的完整架构、功能模块、部署方案和开发指南。

## 📁 文档列表

### 1. 项目架构文档
- **[项目架构总览](./project_architecture_overview.md)** - 项目整体介绍、技术栈、核心功能
- **[系统调用关系图](./system_call_relationships.md)** - 详细的系统间调用关系和数据流向
- **[数据库设计](./database_design.md)** - 数据库表结构、关系设计、索引策略

### 2. 模块详细文档
- **[API后端结构](./api_backend_structure.md)** - PHP ThinkPHP6后端详细架构
- **[管理后台结构](./admin_frontend_structure.md)** - Vue3管理后台详细架构  
- **[移动端APP结构](./mobile_app_structure.md)** - UniApp移动端详细架构

### 3. 部署运维文档
- **[部署指南](./deployment_guide.md)** - 完整的生产环境部署方案
- **[开发环境配置](./development_setup_guide.md)** - VSCode开发环境配置
- **[SSH连接指南](./ssh_connection_guide.md)** - 服务器SSH连接配置

### 4. Bug修复文档
- **[用户转移Bug修复](./user_transfer_bug_fixes.md)** - 用户转移功能Bug分析和修复方案

## 🏗️ 项目架构概览

```
Facai16 金融投资平台
├── 移动端APP (facai16_app)
│   ├── 技术栈: UniApp + Vue3 + UV-UI
│   ├── 功能: 用户投资、资金管理、团队推广
│   └── 平台: iOS、Android、H5、小程序
│
├── 管理后台 (facai16_admin)  
│   ├── 技术栈: Vue3 + Element Plus + Vuex
│   ├── 功能: 用户管理、项目管理、系统配置
│   └── 部署: SPA单页应用
│
└── API后端 (facai16_api)
    ├── 技术栈: PHP8.1 + ThinkPHP6 + MySQL + Redis
    ├── 架构: 多应用模式 (index前台 + admin后台)
    ├── 功能: 业务逻辑、数据处理、接口服务
    └── 特性: JWT认证、队列任务、分布式锁
```

## 🔧 核心功能模块

### 用户管理系统
- **用户注册/登录** - 手机号注册、多种登录方式
- **实名认证** - 身份证验证、银行卡绑定
- **等级体系** - 用户等级、权益管理
- **推广体系** - 邀请码、多级推广关系

### 投资理财系统
- **项目管理** - 投资项目、收益计算
- **投资记录** - 投资历史、收益统计
- **风险控制** - 投资限额、风险评估
- **自动结算** - 到期自动结算、收益发放

### 资金管理系统
- **充值提现** - 多种支付方式、银行卡提现
- **资金流水** - 详细的资金变动记录
- **转账功能** - 用户间转账、手续费管理
- **余额宝** - 活期理财、随存随取

### 团队管理系统
- **关系链管理** - 多级推广关系、关系转移
- **团队数据** - 团队规模、投资统计
- **奖励机制** - 推广奖励、团队奖励
- **数据统计** - 团队收益、业绩排行

### 运营管理系统
- **内容管理** - 文章、公告、轮播图
- **活动管理** - 签到、抽奖、积分商城
- **数据统计** - 用户数据、投资数据、收益数据
- **系统配置** - 参数配置、功能开关

## 🚀 技术特色

### 后端技术特色
- **多应用架构** - 前台API和后台管理分离
- **分层架构** - Controller → Service → Repository 清晰分层
- **策略模式** - 短信、支付、认证等服务的策略化实现
- **队列任务** - 异步处理收益计算、消息推送等
- **分布式锁** - Redis实现的分布式锁防止并发问题
- **数据缓存** - Redis缓存提升系统性能

### 前端技术特色
- **响应式设计** - 适配各种屏幕尺寸
- **组件化开发** - 高度复用的组件库
- **状态管理** - Vuex统一状态管理
- **权限控制** - 基于角色的权限管理
- **富文本编辑** - WangEditor集成

### 移动端技术特色
- **跨平台开发** - 一套代码多端运行
- **原生性能** - 接近原生应用的性能体验
- **离线缓存** - 关键数据本地缓存
- **推送通知** - 实时消息推送
- **生物识别** - 指纹、面部识别登录

## 📊 数据库设计亮点

### 用户关系设计
- **多级关系** - v1_id、v2_id、v3_id 三级上级关系
- **关系转移** - 支持用户关系链的动态调整
- **循环检测** - 防止关系链形成循环引用

### 资金安全设计
- **事务保证** - 所有资金操作使用数据库事务
- **流水记录** - 详细记录每笔资金变动
- **余额校验** - 多重校验确保资金安全

### 性能优化设计
- **索引优化** - 针对查询场景优化的索引设计
- **分表策略** - 大数据表按时间分表
- **读写分离** - 支持主从数据库读写分离

## 🔒 安全机制

### 认证安全
- **JWT Token** - 无状态的用户认证
- **密码加密** - 密码哈希存储
- **二次验证** - 重要操作的二次验证

### 数据安全
- **SQL注入防护** - ORM框架防止SQL注入
- **XSS防护** - 前端数据过滤和转义
- **CSRF防护** - CSRF Token验证

### 业务安全
- **分布式锁** - 防止并发操作导致的数据不一致
- **操作日志** - 详细记录用户操作日志
- **风险控制** - 异常操作检测和限制

## 📈 性能优化

### 缓存策略
- **Redis缓存** - 热点数据缓存
- **页面缓存** - 静态页面缓存
- **接口缓存** - API响应缓存

### 数据库优化
- **索引优化** - 查询性能优化
- **查询优化** - SQL语句优化
- **连接池** - 数据库连接池管理

### 前端优化
- **代码分割** - 按需加载减少首屏时间
- **资源压缩** - 静态资源压缩
- **CDN加速** - 静态资源CDN分发

## 📞 技术支持

如有技术问题，请参考相应的详细文档或联系开发团队。

---

**文档版本**: v1.0  
**最后更新**: 2025-01-27  
**维护团队**: Facai16开发团队
