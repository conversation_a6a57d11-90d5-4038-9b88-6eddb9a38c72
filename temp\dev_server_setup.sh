#!/bin/bash
# 开发服务器环境配置脚本

echo "配置开发服务器环境..."

# 1. 安装必要的软件包
sudo apt update
sudo apt install -y nginx php8.1-fpm php8.1-mysql php8.1-redis php8.1-curl php8.1-json php8.1-mbstring php8.1-xml php8.1-zip php8.1-gd php8.1-xdebug

# 2. 配置Xdebug用于远程调试
echo "配置Xdebug..."
sudo tee /etc/php/8.1/mods-available/xdebug.ini > /dev/null <<EOF
zend_extension=xdebug.so
xdebug.mode=debug
xdebug.start_with_request=yes
xdebug.client_host=your-local-ip
xdebug.client_port=9003
xdebug.log=/var/log/xdebug.log
xdebug.idekey=VSCODE
EOF

# 3. 重启PHP-FPM
sudo systemctl restart php8.1-fpm

# 4. 配置Nginx虚拟主机
sudo tee /etc/nginx/sites-available/facai16-dev > /dev/null <<EOF
server {
    listen 80;
    server_name facai16-dev.local;
    root /var/www/html/facai16/facai16_api/public;
    index index.php index.html;

    # 日志配置
    access_log /var/log/nginx/facai16-dev.access.log;
    error_log /var/log/nginx/facai16-dev.error.log;

    location / {
        try_files \$uri \$uri/ /index.php?\$query_string;
    }

    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.1-fpm.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME \$realpath_root\$fastcgi_script_name;
        include fastcgi_params;
        
        # Xdebug配置
        fastcgi_param XDEBUG_CONFIG "idekey=VSCODE";
    }

    location ~ /\.ht {
        deny all;
    }
}
EOF

# 5. 启用站点
sudo ln -sf /etc/nginx/sites-available/facai16-dev /etc/nginx/sites-enabled/
sudo nginx -t && sudo systemctl reload nginx

# 6. 设置权限
sudo chown -R www-data:www-data /var/www/html/facai16
sudo chmod -R 755 /var/www/html/facai16

echo "开发服务器配置完成！"
